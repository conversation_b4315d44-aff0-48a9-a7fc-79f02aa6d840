// React core
import React, { useEffect, useRef, useState } from "react";
// Third-party library imports
// Services
import type { GameProgress as GameProgressType } from "../services/ConversationStorage";
// Components
import { MessageBubble } from "./MessageBubble";
import { ConversationStorage } from "../services/ConversationStorage";
// Utils & Constants & Helpers
import { useRealTimeConversation } from "../hooks/useRealTimeConversation";
import type { ConversationMessage } from "../utils/conversationUtils";
// Styles

const UnsupportedBrowserMessage = () => (
  <div className="unsupported-browser-warning">
    <h4>⚠️ Reconocimiento de voz no disponible</h4>
    <p>Tu navegador no soporta esta funcionalidad</p>
  </div>
);

interface SimpleVoiceChatProps {
  generatedCharacter?: string;
  isGameStarted: boolean;
  initialMessage?: string;
  onGameEnd?: (gameWon: boolean) => void;
}

export const SimpleVoiceChat: React.FC<SimpleVoiceChatProps> = ({
  generatedCharacter,
  isGameStarted,
  initialMessage,
  onGameEnd,
}) => {
  // State for game management
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);
  const gameEndTriggered = useRef(false);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Hook for real-time conversation management
  const {
    isActive,
    messages,
    isSupported,
    error,
    stopConversation,
    addInitialMessage,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    gameProgress?.gameFinished
  );

  /**
   * Get appropriate empty state message based on current state
   */
  const getEmptyStateMessage = () => {
    if (isActive) {
      return "¡Conversación activa! Puedes empezar a hablar";
    }
    if (isGameStarted && initialMessage) {
      return "Activando chat de voz automáticamente...";
    }
    return "Inicia la conversación para comenzar a chatear";
  };

  /**
   * Add initial message when it arrives
   */
  useEffect(() => {
    if (initialMessage && isGameStarted) {
      addInitialMessage(initialMessage);
    }
  }, [initialMessage, isGameStarted, addInitialMessage]);

  /**
   * Update game progress when session changes
   */
  useEffect(() => {
    if (!isGameStarted) return;

    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);

      // Notify parent when game finishes (only once)
      if (progress?.gameFinished && !gameEndTriggered.current && onGameEnd) {
        console.log("🎮 Game finished detected:", {
          gameFinished: progress.gameFinished,
          gameWon: progress.gameWon,
        });
        gameEndTriggered.current = true;
        onGameEnd(progress.gameWon);
      }
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, [isGameStarted, messages, onGameEnd]);

  /**
   * Reset flags when game restarts
   */
  useEffect(() => {
    if (!isGameStarted) {
      gameEndTriggered.current = false;
    }
  }, [isGameStarted]);

  // Helper to check if game has finished
  const isGameFinished = gameProgress?.gameFinished ?? false;

  /**
   * Stop conversation when game finishes
   */
  useEffect(() => {
    if (isGameFinished && isActive) {
      console.log("🔇 Game finished, stopping conversation");
      stopConversation();
    }
  }, [isGameFinished, isActive, stopConversation]);

  /**
   * Debug: Log messages when they change
   */
  useEffect(() => {
    console.log("🔍 [SimpleVoiceChat] Messages updated:", {
      count: messages.length,
      messages: messages.map(m => ({ id: m.id, type: m.type, content: m.content.substring(0, 50) + '...' }))
    });
  }, [messages]);

  if (!isGameStarted) {
    return null;
  }

  if (!isSupported) {
    return <UnsupportedBrowserMessage />;
  }

  return (
    <div className="voice-chat-container">
      <div className="character-title">
        <span>{generatedCharacter}</span>
      </div>

      {/* Debug info temporal */}
      <div style={{
        fontSize: "12px",
        color: "#666",
        marginBottom: "10px",
        padding: "10px",
        backgroundColor: "#fff3cd",
        border: "1px solid #ffeaa7",
        borderRadius: "4px"
      }}>
        <strong>Debug:</strong> Mensajes: {messages.length} | Activo: {isActive ? "Sí" : "No"} | Error: {error || "Ninguno"}
        <br />
        <strong>Mensajes:</strong> {JSON.stringify(messages.map(m => ({ id: m.id, type: m.type, content: m.content.substring(0, 20) + '...' })))}
      </div>

      {/* Error display */}
      {error && <div className="error-message" style={{ color: "red", marginBottom: "10px" }}>❌ {error}</div>}

      {/* CSS for animations */}
      <style>{`
        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>

      {/* Chat area */}
      <div
        style={{
          border: "1px solid #e9ecef",
          borderRadius: "12px",
          padding: "20px",
          minHeight: "300px",
          maxHeight: "600px",
          overflowY: "auto",
          scrollBehavior: "smooth",
          backgroundColor: "#f8f9fa",
        }}
      >
        {messages.length === 0 ? (
          <EmptyState message={getEmptyStateMessage()} />
        ) : (
          <MessageList messages={messages} />
        )}
      </div>
    </div>
  );
};

/**
 * Empty state component when no messages are present
 */
interface EmptyStateProps {
  message: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({ message }) => (
  <div
    style={{
      textAlign: "center",
      color: "#6c757d",
      fontSize: "16px",
      paddingTop: "60px",
    }}
  >
    <div style={{ fontSize: "48px", marginBottom: "16px" }}>💬</div>
    <p style={{ margin: "8px 0" }}>{message}</p>
    <p
      style={{
        fontSize: "13px",
        opacity: 0.8,
        marginTop: "12px",
      }}
    >
      El micrófono se gestiona automáticamente
    </p>
  </div>
);

/**
 * Message list component that renders all messages with auto-scroll
 */
interface MessageListProps {
  messages: ConversationMessage[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages.length]);

  console.log("🔍 [MessageList] Renderizando con", messages.length, "mensajes");

  return (
    <>
      {/* <div style={{
        fontSize: "10px",
        color: "#999",
        marginBottom: "10px",
        padding: "5px",
        backgroundColor: "#e8f5e8",
        border: "1px solid #c3e6c3"
      }}>
        MessageList renderizado con {messages.length} mensajes
      </div> */}

      {messages.map((message, index) => {
        console.log("🔍 [MessageList] Renderizando mensaje", index, message.id, message.type);
        return <MessageBubble key={message.id} message={message} />;
      })}

      {/* Auto-scroll anchor */}
      <div ref={messagesEndRef} style={{ height: "1px" }} />
    </>
  );
};
