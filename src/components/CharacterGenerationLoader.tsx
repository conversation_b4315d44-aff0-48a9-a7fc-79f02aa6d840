// React core
import React from "react";
// Styles
import "./CharacterGenerationLoader.scss";

interface CharacterGenerationLoaderProps {
  currentAttempt?: number;
  maxAttempts?: number;
  currentStep?: string;
  isVisible: boolean;
}

/**
 * Loading component for character generation with Movistar catalog validation
 * Shows progress through multiple attempts and current step
 */
export const CharacterGenerationLoader: React.FC<CharacterGenerationLoaderProps> = ({
  currentAttempt = 1,
  maxAttempts = 5,
  currentStep = "Generando personaje...",
  isVisible
}) => {
  if (!isVisible) return null;

  const progressPercentage = (currentAttempt / maxAttempts) * 100;

  return (
    <div className="character-generation-loader">
      <div className="loader-overlay">
        <div className="loader-content">
          {/* Main spinner */}
          <div className="spinner-container">
            <div className="spinner"></div>
            <div className="spinner-glow"></div>
          </div>

          {/* Title */}
          <h2 className="loader-title">
            🎭 Preparando tu personaje
          </h2>

          {/* Current step */}
          <p className="loader-step">
            {currentStep}
          </p>

          {/* Progress indicator */}
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            <p className="progress-text">
              Intento {currentAttempt} de {maxAttempts}
            </p>
          </div>

          {/* Steps description */}
          <div className="steps-description">
            <div className="step-item">
              <span className="step-icon">🎲</span>
              <span>Generando personaje único</span>
            </div>
            <div className="step-item">
              <span className="step-icon">🔍</span>
              <span>Validando en catálogo Movistar+</span>
            </div>
            <div className="step-item">
              <span className="step-icon">✨</span>
              <span>Preparando experiencia de juego</span>
            </div>
          </div>

          {/* Fun fact */}
          <div className="fun-fact">
            <p>💡 <strong>¿Sabías que?</strong> Estamos asegurándonos de que tu personaje tenga contenido disponible en Movistar+ para una experiencia completa.</p>
          </div>
        </div>
      </div>
    </div>
  );
};
