// Character Generation Loader Styles
.character-generation-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  
  .loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.95));
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .loader-content {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    max-width: 500px;
    width: 100%;
    text-align: center;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .spinner-container {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
  }

  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(136, 255, 213, 0.3);
    border-top: 4px solid #88FFD5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .spinner-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(136, 255, 213, 0.2) 0%, transparent 70%);
    animation: pulse 2s ease-in-out infinite;
  }

  .loader-title {
    color: #88FFD5;
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0 0 15px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .loader-step {
    color: #ffffff;
    font-size: 1.1rem;
    margin: 0 0 25px 0;
    opacity: 0.9;
  }

  .progress-container {
    margin: 25px 0;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #88FFD5, #4ECDC4);
    border-radius: 4px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(136, 255, 213, 0.5);
  }

  .progress-text {
    color: #ffffff;
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.8;
  }

  .steps-description {
    margin: 30px 0;
    text-align: left;
  }

  .step-item {
    display: flex;
    align-items: center;
    margin: 12px 0;
    color: #ffffff;
    font-size: 0.95rem;
    opacity: 0.9;
  }

  .step-icon {
    margin-right: 12px;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
  }

  .fun-fact {
    margin-top: 30px;
    padding: 20px;
    background: rgba(136, 255, 213, 0.1);
    border: 1px solid rgba(136, 255, 213, 0.3);
    border-radius: 12px;
    text-align: left;

    p {
      color: #ffffff;
      font-size: 0.9rem;
      margin: 0;
      line-height: 1.5;
      opacity: 0.9;
    }

    strong {
      color: #88FFD5;
    }
  }

  // Animations
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes pulse {
    0%, 100% { 
      opacity: 0.3;
      transform: scale(1);
    }
    50% { 
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .loader-content {
      padding: 30px 20px;
      margin: 20px;
    }

    .loader-title {
      font-size: 1.5rem;
    }

    .loader-step {
      font-size: 1rem;
    }

    .step-item {
      font-size: 0.9rem;
    }

    .fun-fact p {
      font-size: 0.85rem;
    }
  }

  @media (max-width: 480px) {
    .loader-content {
      padding: 25px 15px;
    }

    .spinner {
      width: 50px;
      height: 50px;
    }

    .spinner-glow {
      width: 70px;
      height: 70px;
      top: -10px;
      left: -10px;
    }

    .loader-title {
      font-size: 1.3rem;
    }
  }
}
